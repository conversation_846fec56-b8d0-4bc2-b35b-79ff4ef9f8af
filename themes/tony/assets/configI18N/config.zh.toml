##########################################
# 站点配置
# 图标: https://RemixIcon.com/

baseURL = "http://example.com/"
title = "HUGO-THEME-TONY"
languageCode = "zh-CN"
defaultContentLanguage = "zh"
hasCJKLanguage = true

# 主题选择
theme = "tony"

# `hugo new` 新建文章自动打开的文本编辑器
newContentEditor = ""

# 摘要的字数限制
summaryLength = 235

# 是否开启 GitHub 风格的 Emoji 书写方式
enableEmoji = true

# 每一分页的文章数
paginate = 10

# 作者信息
[author]
    # 名字
    name = "HUGO-THEME-TONY"
    # 邮箱
    email = "<EMAIL>"
    # 座右铭或简介
    motto = "HUGO THEME TONY"
    # 头像
    avatar = "/images/t.jpg"
    # 网站（默认值：baseURL）
    website = "/"
    # GitHub
    github = "

# 页面分类
[taxonomies]
  category = "categories"
  tag = "tags"

##########################################
# 菜单配置

# 菜单内的配置说明如下：
# url           链接地址
# name          文本（留空（""）则无）
# weight        权重

[menu]
    # 菜单栏
    [[menu.main]]
        url = "/"
        name = "首页"
        weight = 1
    [[menu.main]]
        url = "/about/"
        name = "关于"
        weight = 2

[[params.pinned]]
    title = "HUGO-THEME-TONY"
    name = "HUGO-THEME-TONY"
    icon = "ri-code-box-line"
    url = "/"

# Markdown 渲染器
[markup]
    defaultMarkdownHandler = "goldmark"
    [markup.goldmark]
        [markup.goldmark.extensions]
            definitionList = true
            footnote = true
            linkify = true
            strikethrough = true
            table = true
            taskList = true
            typographer = false
        [markup.goldmark.parser]
            attribute = true
            autoHeadingID = true
            autoHeadingIDType = "github"
        [markup.goldmark.renderer]
            hardWraps = true
            unsafe = true
            xHTML = false
    [markup.highlight]
        codeFences = true
        guessSyntax = true
        lineNos = true
        lineNumbersInTable = false
        noClasses = true
        style = "onedark"
    [markup.tableOfContents]
        startLevel = 2
        endLevel = 6
        ordered = true

# Hugo 的输出控制
[outputs]
    page = ["HTML"]
    home = ["HTML", "SectionsRSS", "SectionsAtom"]
    section = ["HTML"]
    # 类别
    taxonomy = ["HTML"]

# Atom 文件格式的媒体类型
[mediaTypes."application/atom+xml"]
    suffixes = ["xml"]

# Tony 主题自定义的 Atom 模板 来自MemE
[outputFormats.SectionsAtom]
    mediaType = "application/atom+xml"
    baseName = "atom"

# Tony 主题自定义的 RSS 模板 来自MemE
[outputFormats.SectionsRSS]
    mediaType = "application/rss+xml"
    baseName = "rss"

# RSS & Atom 文章数限制
[services.rss]
    limit = -1

##########################################
# 主题配置

[params]

    ######################################
    # 站点信息

    # 站点的 LOGO
    siteLogo = "/images/t.jpg"
    
    # 站点描述
    siteDescription = ""

    ######################################
    # 文章分区范围

    # 说明：分区的名字即站点的 content 目录下
    #      的文件夹的名字。

    mainSections = []

    ######################################
    # 版权保护
    
    # 是否开启
    enableCopyright = true

    copyrightName = "CC BY-NC 4.0"
    copyrightLink = "https://creativecommons.org/licenses/by-nc/4.0/"

    ######################################
    # 目录
    
    # 是否开启（全局设置）
    enableToc = true

    ######################################
    # 阅读进度条
    
    # 是否开启（全局设置）
    enableReadingBar = true

    ######################################
    # 文章上下页
    
    # 是否开启（全局设置）
    enableAdjacentPost = true

    ######################################
    # 是否显示 Hugo 和 Tony 的链接

    displayPoweredBy = true

    ######################################
    # Markdown 相关

    # 在新标签页打开外链？
    hrefTargetBlank = true

    ######################################
    # 评论

    # 是否开启（全局设置）
    enableComments = false
    # 说明：文章的 Front Matter 中的 `comments`
    #      的优先级高于此处

    ## Valine
    enableValine = false
    valineVersion = "latest"
    valineAppId = ""
    valineAppKey = ""
    valinePlaceholder = ""
    valinePath = ""
    valineAvatar = "mm"
    valineMeta = ["nick", "mail", "link"]
    valinePageSize = 15
    valineVisitor = true
    valineHighlight = true
    avatarForce = true
    valineRecordIP = true
    valineServerURLs = ""
    valineEmojiCDN = ""
    valineEmojiMaps = """"""
    valineEnableQQ = false
    valineRequiredFields = []
    # 说明：https://valine.js.org/

    ## Waline
    enableWaline = false
    walineServerURL = ""

    ######################################
    # Google Analytics

    enableGoogleAnalytics = false

    # 跟踪代码的类型
    trackingCodeType = ""
    # 说明：gtag 或 analytics

    trackingID = ""

    ######################################
    # Google Site Verification

    googleSiteVerification = ""


    ######################################
    # Baidu推送

    enableBaiduPush = true


