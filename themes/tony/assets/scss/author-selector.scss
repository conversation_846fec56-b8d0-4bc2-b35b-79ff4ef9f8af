/* 统一作者选择器样式 - 修复版 */

// 变量定义
$primary-gold: #ffd700;
$light-gold: #fffbea;
$shadow-gold: rgba(255, 215, 0, 0.18);
$shadow-gold-hover: rgba(255, 215, 0, 0.28);
$transition-fast: 0.2s ease;
$transition-normal: 0.3s ease;

// 主要布局调整
.multi-author-layout {
    .desktop-layout {
        display: flex;
        max-width: 1400px;
        margin: 80px auto 0;
        padding: 0 30px;
        gap: 60px;
        align-items: flex-start;

        @media (max-width: 768px) {
            display: none;
        }
    }

    .mobile-layout {
        display: none;

        @media (max-width: 768px) {
            display: block;
            padding: 16px;
            margin-top: 80px;
        }
    }
}

// 左侧作者区域 - 固定定位
.author-section {
    flex: 0 0 40%;
    min-height: 500px;
    position: relative;
}

// 右侧文章区域
.articles-section {
    flex: 1;
    min-height: 500px;
    max-width: 100%;
    width: 100%;
    // 为第一个文章卡片添加上边距，避免被遮挡
    .article-list-item:first-child {
        margin-top: 20px;
    }
}

// 作者选择器包装器 - 重新设计布局
.author-selector-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 30%;
    width: 100%;
    max-width: 320px;
    margin: 0 auto;
    z-index: 10;

    @media (max-width: 768px) {
        position: relative;
        top: 0;
        max-width: 100%;
        padding: 24px 16px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
    }
}

// 中心区域容器
.center-area {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 25px;
}

// 中心触发器 - 居中定位
.center-trigger {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 20;

    &:hover .center-avatar-img {
        transform: scale(1.05);
    }

    &:active .center-avatar-img {
        transform: scale(0.98);
    }
}

.center-avatar-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.center-avatar-img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid #fff;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    object-fit: cover;
    transition: all $transition-normal;
    display: block;

    &.selecting {
        border-color: $primary-gold;
        box-shadow: 0 0 20px $shadow-gold-hover;
    }

    @media (max-width: 768px) {
        width: 80px;
        height: 80px;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }
}

// 作者信息显示区域 - 分离布局避免重叠
.author-info-display {
    text-align: center;
    max-width: 280px;
    margin: 0 auto;
    padding: 0 10px;
    opacity: 1;
    visibility: visible;
    transition: all $transition-normal;
    z-index: 15;

    @media (max-width: 768px) {
        max-width: 100%;
        padding: 0 16px;
        margin-top: 20px;
    }
}

.author-nickname {
    font-size: 1.3rem;
    font-weight: 600;
    margin: 0 0 10px;
    color: #333;
    line-height: 1.3;

    @media (max-width: 768px) {
        font-size: 1.1rem;
        margin: 0 0 10px;
    }
}

.author-bio {
    font-size: 0.9rem;
    color: #666;
    margin: 0 0 18px;
    line-height: 1.5;

    @media (max-width: 768px) {
        font-size: 0.9rem;
        margin: 0 0 16px;
        text-align: center;
    }
}

.author-social {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;

    a {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #f8f9fa;
        color: #666;
        text-decoration: none;
        transition: all $transition-fast;
        border: 1px solid transparent;

        &:hover {
            background: $primary-gold;
            color: #fff;
            transform: translateY(-2px);
            border-color: $primary-gold;
        }

        &:focus {
            outline: 2px solid $primary-gold;
            outline-offset: 2px;
        }

        i {
            font-size: 0.9rem;
        }
    }
}

// 作者选择器 - 重新定位
.author-selector {
    position: absolute;
    transform: translate(-50%, -50%);
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all $transition-normal;
    z-index: 25;

    &.active {
        opacity: 1;
        visibility: visible;
        pointer-events: all;
    }

    @media (max-width: 768px) {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #fff;
        border-radius: 20px;
        padding: 24px;
        box-shadow: 0 24px 48px rgba(0, 0, 0, 0.4);
        max-width: 85vw;
        max-height: 75vh;
        overflow-y: auto;
        z-index: 1001;
        animation: modalSlideIn 0.3s ease-out;
    }
}

// 多边形布局 - 精确居中
.author-polygon {
    position: relative;
    width: 260px;
    height: 260px;

    @media (max-width: 768px) {
        position: static;
        width: 100%;
        height: auto;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        justify-items: center;
        padding: 0;
        max-width: 280px;
        margin: 0 auto;
    }
}

// 作者头像 - 精确定位计算
.author-avatar {
    position: absolute;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transition: all $transition-fast;
    object-fit: cover;
    z-index: 30;

    // 居中偏移量：头像宽度的一半
    margin-left: -10px;
    margin-top: -10px;

    &:hover {
        transform: scale(1.15);
        box-shadow: 0 0 16px $shadow-gold;
        border-color: $primary-gold;
        z-index: 35;
    }

    &.selected {
        border-color: $primary-gold;
        box-shadow: 0 0 20px $shadow-gold-hover;
        transform: scale(1.1);
    }

    &:focus {
        outline: 2px solid $primary-gold;
        outline-offset: 2px;
    }

    @media (max-width: 768px) {
        position: static;
        width: 64px;
        height: 64px;
        margin: 0;
        border-width: 2px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);

        &:hover {
            transform: scale(1.08);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.3);
        }

        &.selected {
            transform: scale(1.05);
            border-color: $primary-gold;
            box-shadow: 0 6px 20px $shadow-gold-hover;
        }

        &:active {
            transform: scale(0.98);
        }
    }
}

// 关闭提示
.close-hint {
    position: absolute;
    top: -45px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    transition: opacity $transition-normal;
    pointer-events: none;
    z-index: 40;

    &::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid rgba(0, 0, 0, 0.8);
    }

    @media (max-width: 768px) {
        position: static;
        transform: none;
        background: #f0f8ff;
        color: #4a5568;
        border-radius: 8px;
        margin-bottom: 20px;
        padding: 12px 16px;
        text-align: center;
        font-size: 0.85rem;
        line-height: 1.4;
        border: 1px solid #e2e8f0;

        &::after {
            display: none;
        }
    }
}

// 移动端遮罩层
.mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 999; // 降低z-index，避免覆盖其他重要元素
    opacity: 0;
    visibility: hidden;
    transition: opacity $transition-normal, visibility $transition-normal;
    pointer-events: none; // 默认不拦截点击事件

    &.active {
        opacity: 1;
        visibility: visible;
        pointer-events: auto; // 激活时才拦截点击事件
    }

    @media (max-width: 768px) {
        display: block;
    }
}

// 响应式断点优化
@media (max-width: 1200px) {
    .multi-author-layout .desktop-layout {
        max-width: 100%;
        gap: 40px;
        padding: 0 20px;
    }

    .author-section {
        flex: 0 0 38%;
    }
}

@media (max-width: 1024px) {
    .multi-author-layout .desktop-layout {
        gap: 30px;
    }

    .author-section {
        flex: 0 0 36%;
    }

    .author-polygon {
        width: 220px;
        height: 220px;
    }

    .author-avatar {
        width: 42px;
        height: 42px;
        margin-left: -21px;
        margin-top: -21px;
    }

    .center-avatar-img {
        width: 100px;
        height: 100px;
    }

    .author-selector-wrapper {
        max-width: 300px;
    }
}

@media (max-width: 900px) {
    .author-section {
        flex: 0 0 35%;
    }

    .author-polygon {
        width: 200px;
        height: 200px;
    }

    .center-avatar-img {
        width: 90px;
        height: 90px;
    }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
    * {
        animation: none !important;
        transition: none !important;
    }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
    .center-avatar-img {
        border-color: #000;

        &.selecting {
            border-color: #000;
            box-shadow: 0 0 0 3px $primary-gold;
        }
    }

    .author-avatar {
        border-color: #000;

        &.selected,
        &:hover {
            border-color: #000;
            box-shadow: 0 0 0 3px $primary-gold;
        }
    }

    .author-social a {
        border: 2px solid #000;

        &:hover {
            border-color: #000;
        }
    }
}

// 暗色模式适配
[data-theme="dark"] {
    .center-avatar-img {
        border-color: #444;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);

        &.selecting {
            border-color: $primary-gold;
        }
    }

    .author-nickname {
        color: #e0e0e0;
    }

    .author-bio {
        color: #b0b0b0;
    }

    .author-social a {
        background: #333;
        color: #b0b0b0;

        &:hover {
            background: $primary-gold;
            color: #000;
        }
    }

    .author-avatar {
        border-color: #444;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    .close-hint {
        background: rgba(255, 255, 255, 0.9);
        color: #333;

        &::after {
            border-top-color: rgba(255, 255, 255, 0.9);
        }

        @media (max-width: 768px) {
            background: #333;
            color: #e0e0e0;
        }
    }

    .author-selector {
        @media (max-width: 768px) {
            background: #2d2d2d;
            box-shadow: 0 24px 48px rgba(0, 0, 0, 0.7);
        }
    }

    .close-hint {
        @media (max-width: 768px) {
            background: #374151;
            color: #d1d5db;
            border-color: #4b5563;
        }
    }
}

// 移动端模态动画
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

// 移动端文章区域优化
.mobile-articles-section {
    @media (max-width: 768px) {
        margin-top: 20px;

        .article-list-item:first-child {
            margin-top: 0;
        }
    }
}

// 移动端触摸优化
@media (max-width: 768px) {
    .center-trigger {
        padding: 8px;
        border-radius: 50%;

        &:active {
            background: rgba(255, 215, 0, 0.1);
        }
    }

    .author-avatar {
        // 增加触摸目标大小
        &::before {
            content: "";
            position: absolute;
            top: -8px;
            left: -8px;
            right: -8px;
            bottom: -8px;
            border-radius: 50%;
            background: transparent;
        }
    }
}
