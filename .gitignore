# Hugo output
public/

# System files
.DS_Store
Thumbs.db

# Editor settings
.vscode/
.idea/

# Tools
tools/

# Temporary files
*.tmp
*.log
*.pyc

# Node.js dependencies
node_modules/

# AI Agent files
.augment/
.roo/

# Obsidian configuration
content/.obsidian/
# Now I wanna use GitHub to make a copy of my Ob configs

# Ob plugin indexing temp files
content/.smtcmp_json_db
content/.smtcmp_vector_db.tar.gz
