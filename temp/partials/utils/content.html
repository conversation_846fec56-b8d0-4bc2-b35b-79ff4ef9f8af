{{- $Deliver := . -}}
{{- $raw := .Content -}}
{{- partial "utils/markdownify.html" (dict "Deliver" . "raw" $raw "isContent" true) -}}

<!-- Image & Video Caption -->
{{- $Content := .Scratch.Get "Content" -}}
{{- if .Site.Params.enableCaption -}}
    {{- $captionPrefix := .Site.Params.captionPrefix -}}
    {{- $regexPatternCaption := `(<(img|video).+) title="([^"]+)"( controls)?( class=".+")?( />|>)(</video>)?` -}}
    {{- $regexReplacementCaption := (printf `$1$4$5$6<p><span style="color: #808080;">%s $3</span></p>` $captionPrefix) -}}
    {{- $Content := $Content | replaceRE $regexPatternCaption $regexReplacementCaption | safeHTML -}}
    {{- .Scratch.Set "Content" $Content -}}
{{- end -}}

<!-- Custom -->
{{- partial "custom/content.html" . -}}

<!-- Final Content -->
{{- $Content := .Scratch.Get "Content" -}}
{{- .Scratch.Set "Content" $Content -}}
