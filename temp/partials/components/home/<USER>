<div id="header_info" class="index-top">
    <nav class="header-nav reveal">
        <img src="{{ $.Site.Params.avatar | relURL }}" class="header-avatar-top">
        <a href="/" title="Spixed" class="header-logo" style="text-decoration: none;">{{ $.Site.Params.author }}</a>
        <p class="lead" style="margin-top: 0px; margin-left: 5px;">{{ $.Site.Params.motto }}</p>
    </nav>
    {{ with .Site.Params.pinned }}
        <div class="index-cates">
            {{ range . }}
                <a href="{{ .url }}" class="" title="{{ .title }}">
                    <li class="cat-item cat-item-4 cat-real" style="display: inline-block;">
                        <div class="header-item-icon-div">
                            <i class="{{ .icon }}"></i>
                        </div>
                        {{ .name }}
                    </li>
                </a>
            {{ end }}
        </div>
    {{ end }}
    <div class="tags-div">
        <a class="tags-scroll-right scroll-left" id="tags-scroll-left">
            <i class="ri-arrow-left-line"></i>
        </a>
        <ul class="post_tags" id="post_tags">
            {{ range .Site.Taxonomies.tags }}
                <li class="cat-real">
                    <a href="{{ .Page.Permalink }}" title="{{ .Page.Title }}">#{{ .Page.Title }}</a>
                </li>
            {{ end }}
        </ul>
        <a class="tags-scroll-right" id="tags-scroll-right">
            <i class="ri-arrow-right-line"></i>
        </a>
    </div>
</div>