{{ .Scratch.Set "prev" .Prev }}
{{ .Scratch.Set "next" .Next }}
{{ $prev := .Scratch.Get "prev" }}
{{ $next := .Scratch.Get "next" }}
{{ if or $prev $next }}
    {{ if and $next (not $next.Params.status) }}
        <div>
            <div class="index-div-prev">
                <h4>{{ i18n "prevPage" }}</h4>
                <p>
                    <a href="{{ $next.RelPermalink }}" rel="next">{{ printf `%s` $next.LinkTitle | safeHTML }}</a>
                </p>
            </div>
        </div>
    {{ end }}
    {{ if and $prev (not $prev.Params.status) }}
        <div>
            <div class="index-div-next">
                <h4>{{ i18n "nextPage" }}</h4>
                <p>
                    <a href="{{ $prev.RelPermalink }}" rel="prev">{{ printf `%s` $prev.LinkTitle | safeHTML }}</a>
                </p>
            </div>
        </div>
    {{ end }}
{{ end }}