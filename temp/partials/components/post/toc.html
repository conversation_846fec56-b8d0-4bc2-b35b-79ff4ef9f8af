{{ if .Params.toc | default .Site.Params.enableToc }}
        <!-- ignore empty links with + -->
        {{ $headers := findRE "<h[1-6].*?>(.|\n])+?</h[1-6]>" .Content }}
        <!-- at least one header to link to -->
        {{ if ge (len $headers) 1 }}
            {{ $firstHeadingLevel := findRE "<h[1-6]" (index $headers 0) 1 }}
            {{ $firstHeadingLevelNum := 0 }}
            {{ if $firstHeadingLevel }}{{ $firstHeadingLevelNum = (replaceRE "[^1-6]" "" (index $firstHeadingLevel 0)) | int }}{{ end }}

            <div class="index-div">
                <div class="single-index">
                    <h4>
                        <i class="ri-list-check-2"></i>
                        {{ i18n "postToc" }}
                    </h4>
                </div>  
                <ul id="article-index" class="index-ul">
                    {{ range $i, $header := $headers }}
                        {{ $currentHeadingLevel := findRE "<h[1-6]" $header 1 }}
                        {{ $currentHeadingLevelNum := 0 }}
                        {{ if $currentHeadingLevel }}{{ $currentHeadingLevelNum = (replaceRE "[^1-6]" "" (index $currentHeadingLevel 0)) | int }}{{ end }}

                        {{/* Calculate the adjusted heading level relative to the first heading in the document */}}
                        {{ $adjustedLevel := sub $currentHeadingLevelNum (sub $firstHeadingLevelNum 1) }}

                        {{/* Only display headings that are at or deeper than the first heading level */}}
                        {{ if ge $adjustedLevel 1 }}
                            {{ $indent := mul (sub $adjustedLevel 1) 20 }} {{/* Indent 20px per level */}}
                            {{ $anchorId := (replaceRE ".* id=\"(.*?)\".*" "$1" $header ) }}
                            <li style="{{ printf "padding-left:%dpx;" $indent }}" class="{{ printf "toc-level-%d" $adjustedLevel }}">
                                <a href="#{{ $anchorId }}">
                                    <i class="ri-record-circle-fill"></i>
                                    &nbsp;{{ $header | plainify | htmlUnescape }}
                                </a>
                            </li>
                        {{ end }}
                    {{ end }}
                </ul>
            </div>
        {{ end }}
{{- end -}}