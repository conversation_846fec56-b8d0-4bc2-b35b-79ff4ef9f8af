<link rel="stylesheet" href="{{ "/css/bootstrap.min.css" | relURL }}">
<link rel="stylesheet" href="{{ "/css/remixicon/remixicon.css" | relURL }}">

{{- $path := (strings.TrimPrefix "/" (printf `%s/css/tony.min.css` .Site.LanguagePrefix)) -}}
{{- $options := (dict "targetPath" $path "outputStyle" "compressed") -}}
{{- $style := resources.Get "scss/main.scss" | resources.ExecuteAsTemplate "/styles/main-rendered.scss" . | css.Sass $options | resources.Fingerprint -}}
{{- printf `<link rel="stylesheet" href="%s" integrity="%s"/>` $style.RelPermalink $style.Data.Integrity | safeHTML -}}
