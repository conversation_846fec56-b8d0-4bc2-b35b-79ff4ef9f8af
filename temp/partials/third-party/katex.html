<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.11.0/dist/katex.min.css" integrity="sha384-BdGj8xC2eZkQaxoQ8nSLefg4AV4/AwB3Fj+8SUSo7pnKP6Eoy18liIKTPn9oBYNG" crossorigin="anonymous" />
<script>
    if (typeof renderMathInElement === 'undefined') {
        var getScript = (options) => {
            var script = document.createElement('script');
            script.defer = true;
            script.crossOrigin = 'anonymous';
            Object.keys(options).forEach((key) => {
                script[key] = options[key];
            });
            document.body.appendChild(script);
        };
        getScript({
            src: 'https://cdn.jsdelivr.net/npm/katex@0.11.0/dist/katex.min.js',
            integrity: 'sha384-JiKN5O8x9Hhs/UE5cT5AAJqieYlOZbGT3CHws/y97o3ty4R7/O5poG9F3JoiOYw1',
            onload: () => {
                getScript({
                    src: 'https://cdn.jsdelivr.net/npm/katex@0.11.0/dist/contrib/mhchem.min.js',
                    integrity: 'sha384-oa0lfxCGjaU1LdYckhq8LZcP+JTf8cyJXe69O6VE6UrShzWveT6KiCElJrck/stm',
                    onload: () => {
                        getScript({
                            src: 'https://cdn.jsdelivr.net/npm/katex@0.11.0/dist/contrib/auto-render.min.js',
                            integrity: 'sha384-kWPLUVMOks5AQFrykwIup5lo0m3iMkkHrD0uJ4H5cjeGihAutqP0yW0J6dpFiVkI',
                            onload: () => {
                                renderKaTex();
                            }
                        });
                    }
                });
            }
        });
    } else {
        renderKaTex();
    }
    function renderKaTex() {
        renderMathInElement(
            document.body,
            {
                delimiters: [
                    {left: "$$", right: "$$", display: true},
                    {left: "\\[", right: "\\]", display: true},
                    {left: "$", right: "$", display: false},
                    {left: "\\(", right: "\\)", display: false}
                ]
            }
        );
    }
</script>