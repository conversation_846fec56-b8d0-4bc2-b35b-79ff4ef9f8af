<script>
    if (typeof MathJax === 'undefined') {
        window.MathJax = {
            loader: {
                load: ['[tex]/mhchem']
            },
            {{ if .Site.Params.disableMathJaxMenu }}
                options: {
                    renderActions: {
                        addMenu: [0, '', '']
                    }
                },
            {{ end }}
            tex: {
                inlineMath: {'[+]': [['$', '$']]},
                tags: 'ams',
                packages: {'[+]': ['mhchem']}
            }
        };
        (function() {
            var script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/mathjax@3.0.1/es5/tex-mml-chtml.js';
            script.defer = true;
            document.head.appendChild(script);
        })();
    } else {
        MathJax.texReset();
        MathJax.typeset();
    }
</script>
