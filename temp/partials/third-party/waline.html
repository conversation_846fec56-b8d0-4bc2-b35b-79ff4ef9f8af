<script type="module">
  import { init } from "https://unpkg.com/@waline/client@latest/dist/waline.js";
  import katex from "https://unpkg.com/katex@0.16/dist/katex.mjs";

  init({
    el: "#waline",
    serverURL: "{{ .Site.Params.walineServerURL }}",
    emoji: [
      "https://unpkg.com/@waline/emojis@latest/qq",
      "https://unpkg.com/@waline/emojis@latest/weibo",
      "https://unpkg.com/@waline/emojis@latest/soul-emoji",
    ],
    dark: "html.data-darkreader-scheme",
    texRenderer: (blockMode, tex) =>
      katex.renderToString(tex, {
        displayMode: blockMode,
        throwOnError: false,
      }),
  });
</script>
