<!-- 备份：原DarkReader实现方案 -->
<script lang="javascript" src="https://cdn.jsdelivr.net/npm/darkreader@latest/darkreader.min.js"></script>

<script lang="javascript">
    function getCookie(cname) {
        var name = cname + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i].trim();
            if (c.indexOf(name) == 0) return c.substring(name.length, c.length);
        }
        return "";
    }

    function setCookie(cname, cvalue, exdays) {
        var d = new Date();
        d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
        var expires = "expires=" + d.toGMTString();
        document.cookie = cname + "=" + cvalue + "; " + expires + "; path=/";
    }

    DarkReader.setFetchMethod(window.fetch);
    var icon;

    if (!getCookie("isDark")) {
        setCookie("isDark", false, 30);
    }
    if (!getCookie("lang")) {
        setCookie("lang", "zh-cn", 30);
    }

    if (getCookie("isDark") == "false" || getCookie("isDark") == ""){
        setCookie("isDark", false, 30)
        DarkReader.disable();
        icon = "moon";
    } else {
        DarkReader.enable();
        icon = "sun";
    }

    document.addEventListener("DOMContentLoaded", function () {
        var lang = getCookie("lang");
        let cntURL = window.location.pathname;
        let cntLang = cntURL.split("/")[1];
        // alert(lang + ", " + cntURL + ", " + cntLang);
        if (cntLang == "en" && lang != "en") {
            window.location.pathname = cntURL.replace("/en/", "/");
        } else if (cntLang != "en" && lang == "en") {
            window.location.pathname = "/en" + cntURL;
        }
    });
</script>